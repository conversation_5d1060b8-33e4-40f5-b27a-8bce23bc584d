using ExternalServices;
using Microsoft.OpenApi.Models;
using Microsoft.AspNetCore.HttpOverrides;
using GamesEngine.Settings;
using GamesEngineMocks;
using Puppeteer.EventSourcing;
using GamesEngine.Business.Liquidity.Persistence;
using System.Diagnostics;

namespace LiquidityBIAPI
{
    public class Startup
    {
        public Startup(IWebHostEnvironment env, IConfiguration configuration)
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(env.ContentRootPath)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("customization/appsettings.accounting.json", optional: true)
                .AddJsonFile("customization/appsettings.security.json", optional: true)
                .AddJsonFile("customization/appsettings.kafka.json", optional: true)
                .AddJsonFile("customization/appsettings.error_emails.json", optional: true)
                .AddJsonFile("customization/appsettings.apm.json", optional: true)
                .AddJsonFile("secrets/appsettings.secrets.json", optional: true);
            Configuration = builder.Build();
            Environment = env;
        }

        public IConfiguration Configuration { get; }
        public IWebHostEnvironment Environment { get; }

        public void ConfigureServices(IServiceCollection services)
        {
            var biHistoricalConfig = Configuration.GetSection("BIIntegration").GetSection("DBHistorical");
            var connectionStrings = biHistoricalConfig.GetSection("ConnectionStrings");
            string clickHouseConnStr = connectionStrings.GetValue<string>("ClickHouse");
            string elasticsearchConnStr = Configuration.GetValue<string>("ELKIntegration:elkserver")
                                         ?? connectionStrings.GetValue<string>("Elasticsearch");

            if (!string.IsNullOrEmpty(clickHouseConnStr))
            {
                services.AddScoped<ClickHouseStorage>(provider => new ClickHouseStorage(clickHouseConnStr));
                services.AddScoped<IClickHouseReadableStorage, ClickHouseStorage>(provider => provider.GetRequiredService<ClickHouseStorage>());
            }
            if (!string.IsNullOrEmpty(elasticsearchConnStr))
            {
                services.AddScoped<ElasticsearchStorage>(provider => new ElasticsearchStorage(elasticsearchConnStr));
                services.AddScoped<IElasticsearchReadableStorage, ElasticsearchStorage>(provider => provider.GetRequiredService<ElasticsearchStorage>());
            }

            services.AddControllers();
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "LiquidityBIAPI", Version = "v1" });
            });

            Security.Configure(services, Configuration);

            var biIntegration = Configuration.GetSection("BIIntegration");
            Debug.WriteLine($"biIntegration {biIntegration}");

            Integration.Configure(KafkaMessage.Prefix.NoTransacction, biIntegration);
            Debug.WriteLine($"Integration is ready.");

            services.AddMvc(options => options.EnableEndpointRouting = false);

            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
        }


        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IClickHouseReadableStorage clickHouseQueryStore = null, IElasticsearchReadableStorage elasticsearchQueryStore = null)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                app.UseSwagger();
                app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "LiquidityBIAPI v1"));
            }

            var sectionDairy = Configuration.GetSection("DBDairy");
            var mySQL = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("MySQL");
            var sqlServer = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("SQLServer");
            var dbSelected = sectionDairy.GetValue<string>("DBSelected");
            var scriptBeforeRecovering = Configuration.GetValue<string>("ActionsBeforeRecovering");

            DBDairy dbDairy = new DBDairy();
            dbDairy.DBSelected = dbSelected;
            Integration.DbDairy = dbDairy;

            if (Integration.UseKafka) Consumers.CreateConsumerForTopics(clickHouseQueryStore);

            int numberOfTheMockConfigured = -1;
            if (dbSelected == DatabaseType.MySQL.ToString())
            {
                LiquidityBIAPI.LiquidityBI.EventSourcingStorage(DatabaseType.MySQL, mySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
            }
            else if (dbSelected == DatabaseType.SQLServer.ToString())
            {
                LiquidityBIAPI.LiquidityBI.EventSourcingStorage(DatabaseType.SQLServer, sqlServer, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
            }
            else if (!String.IsNullOrWhiteSpace(dbSelected))
            {
                throw new Exception($"There is no connection for {dbSelected}");
            }
            else
            {
                string mockToStartStr = Configuration["MockToStart"];
                if (!string.IsNullOrEmpty(mockToStartStr) && int.TryParse(mockToStartStr, out int parsedMockIndex))
                {
                    numberOfTheMockConfigured = parsedMockIndex;
                }
                else if (!string.IsNullOrEmpty(mockToStartStr))
                {
                    Debug.WriteLine($"Warning: MockToStart value '{mockToStartStr}' is not a valid integer. Mocks may not run as expected.");
                }

                var DOTNET_RUNNING_IN_CONTAINER = bool.Parse(System.Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") ?? "false");
                if (DOTNET_RUNNING_IN_CONTAINER)
                    LiquidityBIAPI.LiquidityBI.EventSourcingStorage(DatabaseType.MySQL, mySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);

                RunMock(LiquidityBIAPI.LiquidityBI.Actor, numberOfTheMockConfigured);
            }

            app.UseMvc();

            ForwardedHeadersOptions options = new ForwardedHeadersOptions();
            options.ForwardedHeaders = ForwardedHeaders.XForwardedFor;
            app.UseForwardedHeaders(options);

            try
            {
                Debug.WriteLine("Attempting to create database tables if they do not exist...");
                clickHouseQueryStore.CreateTablesIfNotExists("FP");
                Debug.WriteLine("Table creation process completed.");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"An error occurred while creating database tables: {ex.Message}");
            }

            string mockToStartValue = Configuration["MockToStart"];

            int currentMockSetting = -1;
            if (!string.IsNullOrEmpty(mockToStartValue) && int.TryParse(mockToStartValue, out int parsedVal))
            {
                currentMockSetting = parsedVal;
            }

            if (currentMockSetting == 0)
            {
                try
                {
                    Debug.WriteLine("MockToStart is 0. Attempting to insert initial data...");
                    DataSeeder.SeedInitialData(clickHouseQueryStore, "FP");
                    Debug.WriteLine("Initial data insertion process completed.");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"An error occurred while inserting initial data: {ex.Message}");
                }
            }
            else
            {
                Debug.WriteLine($"MockToStart is '{mockToStartValue}' (parsed as {currentMockSetting}), skipping initial data insertion.");
            }
        }

        void RunMock(Actor actor, int index = -1)
        {
            switch (index)
            {
                case 0:
                    LiquidityBIMocks.Init(actor);
                    Debug.WriteLine("Mock 0 initialized.");
                    break;
                default:
                    Debug.WriteLine($"Mock with index {index} is not implemented. Throwing exception.");
                    throw new Exception($"The mock {index} its not implemented yet.");
            }
        }
    }
}
