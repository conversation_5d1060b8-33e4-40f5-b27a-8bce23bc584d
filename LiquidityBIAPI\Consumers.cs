﻿using GamesEngine;
using GamesEngine.Business.Liquidity;
using GamesEngine.Business.Liquidity.Persistence;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System.Diagnostics;
using static GamesEngine.Business.Liquidity.Persistence.DataModel;
using static GamesEngine.PurchaseOrders.CustomerMessage;

namespace LiquidityBIAPI
{
    public class Consumers
    {
        internal static void CreateConsumerForTopics(IStorage storage)
        {
            new ContainerEventsConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForContainerEvents, storage).StartListening();
        }

        public class ContainerEventsConsumer : Consumer
        {
            private string topic;
            private IStorage _storage;

            public ContainerEventsConsumer(string group, string topic, IStorage storage) : base(group, topic)
            {
                this.topic = topic;
                _storage = storage;
            }

            public static LiquidityMessageType GetType(string message)
            {
                return (LiquidityMessageType)(int)message[0];
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                var messageType = GetType(msg);
                switch (messageType)
                {
                    case LiquidityMessageType.LiquidCreated:
                        var createdLiquidMessage = new CreatedLiquidMessage(msg);
                        _storage.CreateTablesIfNotExists(createdLiquidMessage.Kind);
                        _storage.CreateJar(createdLiquidMessage.Kind, version:1, description: $"First Jar {createdLiquidMessage.Kind}", createdLiquidMessage.CreatedAt);

                        break;
                    case LiquidityMessageType.DepositConfirmed:
                        ConfirmDepositMessage confirmDepositMessage = new ConfirmDepositMessage(msg);
                        Deposit depositToInsert = new Deposit(confirmDepositMessage);

                        _storage.CreateDeposit(confirmDepositMessage.Kind, depositToInsert);
                        _storage.CreateJarDetailIfNotExists(confirmDepositMessage.Kind, confirmDepositMessage.JarVersion, depositToInsert.Id, depositToInsert.Created);
                        break;
                    case LiquidityMessageType.JarCreated:
                        CreatedJarMessage createdJarMessage = new CreatedJarMessage(msg);

                        _storage.CreateJar(createdJarMessage.Kind, createdJarMessage.Version, createdJarMessage.Description, createdJarMessage.CreatedAt);

                        break;
                    case LiquidityMessageType.TankCreated:
                        CreatedTankMessage createdTankMessage = new CreatedTankMessage(msg);
                        Tank createdTank = new Tank(createdTankMessage);
                        
                        _storage.CreateTank(createdTankMessage.Kind, createdTank);
                        if (createdTank.DepositIds != null && createdTank.DepositIds.Any())
                        {
                            _storage.CreateTankDetails(createdTankMessage.Kind, createdTank.Id, createdTank.DepositIds, createdTank.Created);
                        }
                            
                        break;
                    case LiquidityMessageType.TankMerged:
                        TankMergedMessage tankMergedMessage = new TankMergedMessage(msg);
                        Tank mergedTank = new Tank(tankMergedMessage);
                        
                        _storage.CreateTank(tankMergedMessage.Kind, mergedTank);
                        if (mergedTank.DepositIds != null && mergedTank.DepositIds.Any())
                        {
                            _storage.CreateTankDetails(tankMergedMessage.Kind, mergedTank.Id, mergedTank.DepositIds, mergedTank.Created);
                        }

                        break;
                    case LiquidityMessageType.TankerCreated:
                        CreatedTankerMessage createdTankerMessage = new CreatedTankerMessage(msg);
                        
                        Tanker tanker = new Tanker(createdTankerMessage);
                        _storage.CreateTanker(createdTankerMessage.Kind, tanker);
                        
                        break;
                    case LiquidityMessageType.DispenserCreated:
                        CreatedDispenserMessage createdDispenserMessage = new CreatedDispenserMessage(msg);
                        Dispenser dispenser = new Dispenser(createdDispenserMessage);
                        _storage.CreateDispenser(createdDispenserMessage.Kind, dispenser);
                        break;
                    default:
                        var e = new GameEngineException($"There is no implementation for {nameof(messageType)} {messageType}");
                        Loggers.GetIntance().Emails.Error($"Unknown LiquidityMessageType encountered: {messageType}", e);
                        throw e;
                }
                
            }
        }

        public class ClickHouseContainerEventsConsumer : ContainerEventsConsumer
        {
            public ClickHouseContainerEventsConsumer(string group, string topic, IStorage storage)
                : base(group, topic, storage) { }
        }

        public class ElasticsearchContainerEventsConsumer : ContainerEventsConsumer
        {
            public ElasticsearchContainerEventsConsumer(string group, string topic, IStorage storage)
                : base(group, topic, storage) { }
        }


        internal static void CreateConsumerForClickHouse(IStorage clickHouseStorage, IConfiguration configuration)
        {
            new ClickHouseContainerEventsConsumer(Integration.Kafka.Group + "_clickhouse", Integration.Kafka.TopicForContainerEvents, clickHouseStorage).StartListening();
            Debug.WriteLine($"Started ClickHouse Consumer. Group: {Integration.Kafka.Group + "_clickhouse"}, Topic: {Integration.Kafka.TopicForContainerEvents}");
        }

        internal static void CreateConsumerForElasticsearch(IStorage elasticsearchStorage, IConfiguration configuration)
        {
            new ElasticsearchContainerEventsConsumer(Integration.Kafka.Group + "_elasticsearch", Integration.Kafka.TopicForContainerEvents, elasticsearchStorage).StartListening();
            Debug.WriteLine($"Started Elasticsearch Consumer. Group: {Integration.Kafka.Group + "_elasticsearch"}, Topic: {Integration.Kafka.TopicForContainerEvents}");
        }
    }
}
