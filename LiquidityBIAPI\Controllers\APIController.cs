using GamesEngine;
using GamesEngine.Business.Liquidity.Persistence;
using GamesEngine.Custodian;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Mvc;
using System.Runtime.Serialization;
using static GamesEngine.Business.Liquidity.Persistence.DataModel;

namespace LiquidityBIAPI.Controllers
{
    [ApiController]
    [Route("api/liquiditybi")]
    public class APIController : AuthorizeController
    {
        private readonly IClickHouseReadableStorage _clickHouseQueryStore;
        private readonly IElasticsearchReadableStorage _elasticsearchQueryStore;

        public APIController(IClickHouseReadableStorage clickHouseQueryStore = null,
                             IElasticsearchReadableStorage elasticsearchQueryStore = null) 
        {
            _clickHouseQueryStore = clickHouseQueryStore; 
            _elasticsearchQueryStore = elasticsearchQueryStore;
        }

        [HttpGet("{currencyCode}/jar/deposits")]
        public ActionResult<DepositsInNewestJarResponse> DepositsInNewestJar(string currencyCode, DateTime startDate, DateTime endDate, string accountNumber = null)
        {
            try
            {
                List<Deposit> deposits = _elasticsearchQueryStore.DepositsInNewestJar(currencyCode, startDate, endDate, accountNumber);
                IEnumerable<DepositSummaryResponse> depositSummaries = deposits.Select(d => new DepositSummaryResponse
                {
                    Date = d.DateAsText,
                    DomainId = d.DomainId,
                    AccountNumber = d.AccountNumber,
                    Amount = d.Amount,
                    DocumentNumber = d.DocumentNumber,
                    Address = d.Address
                });

                decimal totalAmount = deposits.Sum(d => d.Amount);
                var response = new DepositsInNewestJarResponse
                {
                    TotalAmount = totalAmount,
                    Deposits = depositSummaries
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while retrieving transactions.");
            }
        }

        [HttpGet("{currencyCode}/tanks/{tankId}/deposits")]
        public ActionResult<TankDepositsResponse> GetDepositsByTank(string currencyCode, long tankId)
        {
            if (tankId <= 0) return BadRequest($"{nameof(tankId)} {tankId} is not valid.");

            try
            {
                TankWithDeposits tankData = _elasticsearchQueryStore.TankAndAllItsDeposits(currencyCode, tankId);

                if (tankData == null || tankData.TankInfo == null) return NotFound($"{nameof(tankId)} {tankId} is not found.");

                IEnumerable<TankDepositSummaryResponse> depositSummaries = tankData.Deposits.Select(d => new TankDepositSummaryResponse
                {
                    Date = d.DateAsText,
                    Amount = d.Amount,
                    DocumentNumber = d.DocumentNumber,
                    StoreId = d.StoreId,
                    AccountNumber = d.AccountNumber
                });

                decimal totalAmount = tankData.Deposits.Sum(d => d.Amount);
                var responseDto = new TankDepositsResponse
                {
                    TankId = tankData.TankInfo.Id,
                    TankDescription = tankData.TankInfo.Description,
                    TotalAmount = totalAmount,
                    Deposits = depositSummaries
                };

                return Ok(responseDto);
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, $"An error occurred while retrieving deposit information for Tank ID {tankId}.");
            }
        }

        [HttpGet("{currencyCode}/tankers/{tankerId}/deposits")]
        public ActionResult<TankerDepositsResponse> DepositsByTanker(string currencyCode, long tankerId)
        {
            if (tankerId <= 0) return BadRequest($"{nameof(tankerId)} {tankerId} is not valid.");

            try
            {
                TankerWithDeposits tankerData = _elasticsearchQueryStore.TankerAndAllItsDeposits(currencyCode, tankerId);
                if (tankerData == null || tankerData.TankerInfo == null) return NotFound($"{nameof(tankerId)} {tankerId} is not found.");

                IEnumerable<TankerDepositSummaryResponse> depositSummaries = tankerData.Deposits.Select(d => new TankerDepositSummaryResponse
                {
                    Date = d.DateAsText,
                    Amount = d.Amount,
                    DocumentNumber = d.DocumentNumber,
                    StoreId = d.StoreId,
                    AccountNumber = d.AccountNumber
                });

                decimal totalAmount = tankerData.Deposits.Sum(d => d.Amount);
                var responseDto = new TankerDepositsResponse
                {
                    TankerId = tankerData.TankerInfo.Id,
                    TankerDescription = tankerData.TankerInfo.Description,
                    TotalAmount = totalAmount,
                    Deposits = depositSummaries
                };

                return Ok(responseDto);
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, $"An error occurred while retrieving deposit information for Tanker ID {tankerId}.");
            }
        }

        [HttpGet("{currencyCode}/dispensers/{dispenserId}/withdrawals")]
        public ActionResult<DispenserWithdrawalsResponse> GetWithdrawalsByDispenser(string currencyCode, long dispenserId)
        {
            if (dispenserId <= 0) return BadRequest($"{nameof(dispenserId)} {dispenserId} is not valid.");

            try
            {
                var dispenserData = _elasticsearchQueryStore.DispenserAndAllItsWithdrawals(currencyCode, dispenserId);

                if (dispenserData == null || dispenserData.DispenserInfo == null) return NotFound($"{nameof(dispenserId)} {dispenserId} is not found.");

                var withdrawalSummaries = dispenserData.Withdrawals.Select(w => new DispenserWithdrawalSummaryResponse
                {
                    Date = w.DateAsText,
                    Amount = w.Amount,
                    DocumentNumber = w.DocumentNumber,
                    StoreId = w.StoreId,
                    AccountNumber = w.AccountNumber
                });

                decimal totalAmount = dispenserData.Withdrawals.Sum(w => w.Amount);
                var responseDto = new DispenserWithdrawalsResponse
                {
                    DispenserId = dispenserData.DispenserInfo.Id,
                    DispenserDescription = dispenserData.DispenserInfo.Description,
                    TotalAmount = totalAmount,
                    Withdrawals = withdrawalSummaries
                };

                return Ok(responseDto);
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, $"An error occurred while retrieving withdrawal information for Dispenser ID {dispenserId}.");
            }
        }

        [DataContract(Name = "DepositsInNewestJarResponse")]
        public class DepositsInNewestJarResponse
        {
            [DataMember(Name = "totalAmount")]
            public decimal TotalAmount { get; set; }

            [DataMember(Name = "depositCount")]
            public int DepositCount => Deposits?.Count() ?? 0;

            [DataMember(Name = "deposits")]
            public IEnumerable<DepositSummaryResponse> Deposits { get; set; }
        }

        [DataContract(Name = "DepositSummaryResponse")]
        public class DepositSummaryResponse
        {
            [DataMember(Name = "date")]
            public string Date { get; set; }
            [DataMember(Name = "domainId")]
            public int DomainId { get; set; }
            [DataMember(Name = "accountNumber")]
            public string AccountNumber { get; set; }
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "documentNumber")]
            public string DocumentNumber { get; set; }
            [DataMember(Name = "address")]
            public string Address { get; set; }
        }

        [DataContract(Name = "TankDepositSummaryResponse")]
        public class TankDepositSummaryResponse
        {
            [DataMember(Name = "date")]
            public string Date { get; set; }
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "documentNumber")]
            public string DocumentNumber { get; set; }
            [DataMember(Name = "storeId")]
            public int StoreId { get; set; }
            [DataMember(Name = "accountNumber")]
            public string AccountNumber { get; set; }
        }

        [DataContract(Name = "TankDepositsResponse")]
        public class TankDepositsResponse
        {
            [DataMember(Name = "tankId")]
            public long TankId { get; set; }
            [DataMember(Name = "tankDescription")]
            public string TankDescription { get; set; }
            [DataMember(Name = "totalAmount")]
            public decimal TotalAmount { get; set; }
            [DataMember(Name = "depositCount")]
            public int DepositCount => Deposits?.Count() ?? 0;
            [DataMember(Name = "deposits")]
            public IEnumerable<TankDepositSummaryResponse> Deposits { get; set; }
        }

        [DataContract(Name = "TankerDepositSummaryResponse")]
        public class TankerDepositSummaryResponse
        {
            [DataMember(Name = "date")]
            public string Date { get; set; }
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "documentNumber")]
            public string DocumentNumber { get; set; }
            [DataMember(Name = "storeId")]
            public int StoreId { get; set; }
            [DataMember(Name = "accountNumber")]
            public string AccountNumber { get; set; }
        }

        [DataContract(Name = "TankerDepositsResponse")]
        public class TankerDepositsResponse
        {
            [DataMember(Name = "tankerId")]
            public long TankerId { get; set; }
            [DataMember(Name = "tankerDescription")]
            public string TankerDescription { get; set; }
            [DataMember(Name = "totalAmount")]
            public decimal TotalAmount { get; set; }
            [DataMember(Name = "depositCount")]
            public int DepositCount => Deposits?.Count() ?? 0;
            [DataMember(Name = "deposits")]
            public IEnumerable<TankerDepositSummaryResponse> Deposits { get; set; }
        }

        [DataContract(Name = "DispenserWithdrawalSummaryResponse")]
        public class DispenserWithdrawalSummaryResponse
        {
            [DataMember(Name = "date")]
            public string Date { get; set; }
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "currencyId")]
            public int CurrencyId { get; set; }
            [DataMember(Name = "documentNumber")]
            public string DocumentNumber { get; set; }
            [DataMember(Name = "storeId")]
            public int StoreId { get; set; }
            [DataMember(Name = "accountNumber")]
            public string AccountNumber { get; set; }
        }

        [DataContract(Name = "DispenserWithdrawalsResponse")]
        public class DispenserWithdrawalsResponse
        {
            [DataMember(Name = "dispenserId")]
            public long DispenserId { get; set; }
            [DataMember(Name = "dispenserDescription")]
            public string DispenserDescription { get; set; }
            [DataMember(Name = "totalAmount")]
            public decimal TotalAmount { get; set; }
            [DataMember(Name = "withdrawalCount")]
            public int WithdrawalCount => Withdrawals?.Count() ?? 0;
            [DataMember(Name = "withdrawals")]
            public IEnumerable<DispenserWithdrawalSummaryResponse> Withdrawals { get; set; }
        }
    }
}
