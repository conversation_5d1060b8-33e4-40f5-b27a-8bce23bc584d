﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static GamesEngine.Business.Liquidity.Persistence.DataModel;

namespace GamesEngine.Business.Liquidity.Persistence
{
    public interface IStorage
    {
        void CreateTablesIfNotExists(string kind);
        List<Deposit> DepositsInNewestJar(string kind, DateTime startDate, DateTime endDate, string accountNumber = null);
        TankWithDeposits TankAndAllItsDeposits(string kind, long tankId);
        TankerWithDeposits TankerAndAllItsDeposits(string kind, long tankerId);
        DispenserWithWithdrawals DispenserAndAllItsWithdrawals(string kind, long dispenserId);

        void CreateDeposit(string kind, Deposit depositDetails);
        void CreateJar(string kind, long version, string description, DateTime created);
        void CreateTank(string kind, Tank tank);
        void CreateTanker(string kind, Tanker tanker);
        void CreateJarDetailIfNotExists(string kind, long jarId, long depositId, DateTime created);
        void CreateTankDetailIfNotExists(string kind, long tankId, long depositId, DateTime created);
        void CreateTankDetails(string kind, long tankId, IEnumerable<int> depositIds, DateTime created);
        void CreateTankerDetailIfNotExists(string kind, long tankId, long depositId, DateTime created);

        void CreateWithdrawal(string kind, Withdrawal w);
        void CreateBottle(string kind, Bottle b);
        void CreateDispenser(string kind, Dispenser d);
        void CreateBottleDetailIfNotExists(string kind, long withdrawalId, long bottleId, DateTime created);
        void CreateDispenserDetailIfNotExists(string kind, long withdrawalId, long dispenserId, DateTime created);
    }

    public interface IClickHouseReadableStorage : IStorage { }
    public interface IElasticsearchReadableStorage : IStorage { }
}
